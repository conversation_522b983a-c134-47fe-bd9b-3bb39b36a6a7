package com.desaysv.workserver.service.impl;


import com.desaysv.workserver.aspect.LogExecutionTime;
import com.desaysv.workserver.entity.ColumnNameConstants;
import com.desaysv.workserver.excel.*;
import com.desaysv.workserver.mapper.ExcelDataMapper;
import com.desaysv.workserver.service.ExcelDataHandleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
@Slf4j
@Lazy
public class ExcelDataHandleServiceImpl implements ExcelDataHandleService {
    @Autowired
    private ExcelDataMapper excelDataMapper;
    // 分批处理参数
    private final int batchSize = 500;  // 根据SQLite限制调整批次大小
    @Override
    public void insertData(Map<String, Object> dataInfo) {
        Integer projectId = (Integer) dataInfo.get("projectId");
        String excelFileName = String.valueOf(dataInfo.get("excelFilePath"));
        Map<String, ExcelSheetTable> excelSheetData = (Map<String, ExcelSheetTable>) dataInfo.get("excelSheetData");
        ExcelFileEntity excelFileEntity = new ExcelFileEntity(1, projectId, excelFileName);
        insertExcelFile(excelFileEntity);
        insertExcelSheetAndData(excelSheetData);
    }

    public void insertExcelFile(ExcelFileEntity excelFile) {
        excelDataMapper.truncateExcelFileTable();
        excelDataMapper.insertExcelFile(excelFile);
    }

    //FIXME:耗时
    @LogExecutionTime
    public void insertExcelSheetAndData(Map<String, ExcelSheetTable> excelSheetData) {
        long start = System.currentTimeMillis();
        excelDataMapper.truncateExcelSheetTable();
        excelDataMapper.truncateExcelDataTable();
        ExecutorService executorService = Executors.newFixedThreadPool(excelSheetData.size());

        excelSheetData.forEach((sheetName, data) -> {
            log.info("插入表格名:{}，行数:{}", sheetName, data.getTableData().size());
            executorService.submit(() -> {
                handleExcelSheetData(sheetName, data);
            });
        });

        // 关闭线程池并等待所有任务完成
        executorService.shutdown();
        log.info("插入Excel表格耗时:{}ms", System.currentTimeMillis() - start);
    }

    /*
     * 处理Excel表格数据
     * */
    private void handleExcelSheetData(String sheetName, ExcelSheetTable data) {
        Integer selectedColumnId = findSelectedColumnIndex(data.getTableHeader());
        ExcelSheetEntity sheetModel = new ExcelSheetEntity(1, sheetName);
        excelDataMapper.insertExcelSheet(sheetModel);
        ExcelSheetEntity sheetData = excelDataMapper.findSheetDataBySheetName(sheetName);
        List<HashMap<Integer, String>> sheetTableList = data.getTableData();
        // 每 1000 条数据作为一个任务
        int batchSize = 1000;
        for (int i = 0; i < sheetTableList.size(); i += batchSize) {
            // 将数据插入到数据库中
            insertData(batchSize, i, sheetTableList, sheetData, selectedColumnId);
        }
    }

    /*
     * 将数据插入到数据库中。
     * */
    private void insertData(int batchSize, int startIndex, List<HashMap<Integer, String>> sheetTableList, ExcelSheetEntity sheetData, Integer selectedColumnId) {
        int endIndex = Math.min(startIndex + batchSize, sheetTableList.size());
        ArrayList<ExcelDataEntity> dataEntityList = new ArrayList<>();
        for (int i = startIndex; i < endIndex; i++) {
            ExcelDataEntity excelDataEntity = createExcelDataEntity(sheetTableList, sheetData, selectedColumnId, i);
            dataEntityList.add(excelDataEntity);
        }
        excelDataMapper.insertExcelDataBatch(dataEntityList);
    }

    /*
     *  * 创建数据实体对象。
     * */
    private ExcelDataEntity createExcelDataEntity(List<HashMap<Integer, String>> sheetTableList, ExcelSheetEntity sheetData, Integer selectedColumnId, int i) {
        ExcelDataEntity dataModel = new ExcelDataEntity();
        dataModel.setSheetId(sheetData.getSheetId());
        dataModel.setRowNumber(i + 1);
        HashMap<Integer, String> rowData = sheetTableList.get(i);
        rowData.forEach((columnIndex, columnValue) -> {
//                    if (columnIndex < data.getTableHeader().size()) {
            if (selectedColumnId != -1 && Objects.equals(columnIndex, selectedColumnId)) {
                //FIXME:此处存在问题，保存和导出测试报告时要求根据是否勾选测试，修改值。
                ExcelDataEntity.setColumnValue(dataModel, selectedColumnId + 1, "NO");
            } else {
                ExcelDataEntity.setColumnValue(dataModel, columnIndex + 1, columnValue);
            }
//                    }
        });
        return dataModel;
    }

    private Integer findSelectedColumnIndex(List<String> tableData) {
        return tableData.indexOf(ColumnNameConstants.getInstance().getChoose());
    }


    @Override
    public void updateExcelData(ExcelRowDataModel excelRowDataModel) {
        String sheetName = excelRowDataModel.getSheetName();
        ExcelSheetEntity excelSheet = excelDataMapper.findSheetDataBySheetName(sheetName);
        if (excelSheet == null)
            return;
        Integer sheetId = excelSheet.getSheetId();
        List<String> data = excelRowDataModel.getData();
        ExcelDataEntity dataModel = new ExcelDataEntity();
        dataModel.setSheetId(sheetId);
        dataModel.setRowNumber(excelRowDataModel.getRowNumber());
        for (int i = 0; i < data.size(); i++) {
            ExcelDataEntity.setColumnValue(dataModel, i + 1, data.get(i));
        }
        excelDataMapper.updateExcelData(dataModel);
    }

    @Override
    public void clearMapColumnData(Map<String, Object> columnDataMap) {
        String sheetName = String.valueOf(columnDataMap.get("sheetName"));
        ExcelSheetEntity excelSheet = excelDataMapper.findSheetDataBySheetName(sheetName);
        columnDataMap.put("sheetId", excelSheet.getSheetId());
        excelDataMapper.clearMapColumnData(columnDataMap);
    }

    @Override
    public List<ExcelDataEntity> findExcelDataByName(String sheetName) {
        ExcelSheetEntity sheetData = excelDataMapper.findSheetDataBySheetName(sheetName);
        log.info("表头名:{}, 表格数据:{}", sheetName, sheetData);
        return excelDataMapper.findExcelDataBySheetId(sheetData.getSheetId());
    }

    @Override
    public Integer deleteExcelDataByName(String sheetName) {
        ExcelSheetEntity sheetData = excelDataMapper.findSheetDataBySheetName(sheetName);
        Integer sheetId = sheetData.getSheetId();
        return excelDataMapper.deleteDataById(sheetId);
    }

//    @Override
//    public void insertToExcelDataTable(String excelFileName, String sheetName, List<ExcelRowDataModel> excelRowDataModels) {
//        ExcelSheetEntity sheetData = excelDataMapper.findSheetDataBySheetName(sheetName);
//        Integer sheetId = sheetData.getSheetId();
//        List<ExcelDataEntity> dataModels = new ArrayList<>();
//        for (ExcelRowDataModel excelRowDataModel : excelRowDataModels) {
//            ExcelDataEntity dataModel = new ExcelDataEntity();
//            dataModel.setSheetId(sheetId);
//            dataModel.setRowNumber(excelRowDataModel.getRowNumber());
//            List<String> data = excelRowDataModel.getData();
//            for (int col = 0; col < data.size(); col++) {
//                ExcelDataEntity.setColumnValue(dataModel, col + 1, data.get(col));
//            }
//            dataModels.add(dataModel);
//            //excelDataMapper.insertExcelData(dataModel);
//        }
//        excelDataMapper.insertExcelDataBatch(dataModels);
//    }


    @Override
    public void insertToExcelDataTable(String excelFileName, String sheetName, List<ExcelRowDataModel> excelRowDataModels) {
        ExcelSheetEntity sheetData = excelDataMapper.findSheetDataBySheetName(sheetName);
        Integer sheetId = sheetData.getSheetId();
        List<ExcelDataEntity> batchList = new ArrayList<>(batchSize);
        for (ExcelRowDataModel rowModel : excelRowDataModels) {
            ExcelDataEntity dataModel = new ExcelDataEntity();
            dataModel.setSheetId(sheetId);
            dataModel.setRowNumber(rowModel.getRowNumber());
            // 处理列数据
            List<String> rowData = rowModel.getData();
            for (int colIndex = 0; colIndex < rowData.size(); colIndex++) {
                ExcelDataEntity.setColumnValue(dataModel, colIndex + 1, rowData.get(colIndex));
            }
            // 添加到当前批次
            batchList.add(dataModel);

            // 达到批次大小时执行插入
            if (batchList.size() >= batchSize) {
                try {
                    excelDataMapper.insertExcelDataBatch(batchList);
                    log.info("批量插入数据500条数据成功");
                    batchList.clear();
                } catch (Exception e) {
                    log.error("批量插入数据时发生错误：{}", e.getMessage());
                }
            }
        }

        // 插入最后一批剩余数据
        if (!batchList.isEmpty()) {
            try {
                excelDataMapper.insertExcelDataBatch(batchList);
                batchList.clear();
            } catch (Exception e) {
                log.error("插入最后一批剩余数据时发生错误：{}", e.getMessage());
            }
        }
    }
}
