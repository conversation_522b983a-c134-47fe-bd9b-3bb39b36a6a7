package com.desaysv.workserver.monitor;

import lombok.Getter;

/**
 * 客户端状态值
 */
public class TestClientConstant {

    @Getter
    public enum Status {
        READY(0),
        TESTING(1),
        PAUSING(2),
        COMPLETED(3),
        FAILED(4),
        TERMINATED(5),
        LOGOUT(6),
        EXITED(7),
        COMPLETED_PASS(8),
        COMPLETED_FAIL(9);

        private final int value;

        Status(int value) {
            this.value = value;
        }
    }


    public static String getStatusNameByCode(int statusCode) {
        for (Status e : Status.values()) {
            if (e.getValue() == statusCode) return e.name();
        }
        return null;
    }
}

