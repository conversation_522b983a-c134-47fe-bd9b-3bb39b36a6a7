package com.desaysv.workserver.controller.excel;

import com.desaysv.workserver.annotation.RequestSingleParam;
import com.desaysv.workserver.aspect.LogExecutionTime;
import com.desaysv.workserver.entity.ColumnNameConstants;
import com.desaysv.workserver.excel.*;
import com.desaysv.workserver.model.TestProject;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.service.ExcelDataHandleService;
import com.desaysv.workserver.service.TestProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.desaysv.workserver.config.ExportTypeConstants.EXPORT_ORIGINAL_FILE;

/**
 * Excel管理
 */
@Slf4j
@RestController
@RequestMapping("/excelcase")
@Lazy
public class ExcelCaseMgmtController {
    private Map<String, ExcelSheetTable> tableMap;
    @Autowired
    private ExcelDataHandleService excelDataService;
    @Autowired
    private TestProjectService testProjectService;
    private Map<String, Object> dataMap = new HashMap<>();

    @PostMapping("/loadExcel")
    public ResultEntity<Map<String, ExcelSheetTable>> loadExcel(@RequestBody ExcelEntity excelEntity) {
        log.info("导入Excel表:{}（案例路径:{}）", excelEntity.getSheetNames(), excelEntity.getOriginalFilePath());
        Map<String, ExcelSheetTable> testCaseMap = ExcelUtils.readExcel(excelEntity);
        List<ExcelHeaderOptions> excelHeaderOptions = ExcelUtils.getFilterDropdownValues(excelEntity);
        for (ExcelHeaderOptions excelHeaderOption : excelHeaderOptions) {
            String sheetName = excelHeaderOption.getSheetName();
            if (testCaseMap.containsKey(sheetName)) {
                ExcelSheetTable table = testCaseMap.get(sheetName);
                table.setTableHeaderDropDownOptions(excelHeaderOption.getHeadersOptions());
            }
        }
        this.tableMap = testCaseMap;
        TestProject testProject = testProjectService.getProjectByName(excelEntity.getProjectName());
        dataMap.clear();
        dataMap.put("projectId", testProject.getId());
        dataMap.put("excelFilePath", excelEntity.getOriginalFilePath());
        dataMap.put("excelSheetData", testCaseMap);
//        log.info("导入Excel工作表:{}（案例:{}）", testCaseMap.keySet(), excelEntity.getOriginalFilePath());
        return ResultEntity.ok(testCaseMap);
    }

    //FIXME：测试Excel失败导入后有没有同步到
    @PostMapping("/syncColumnConstantsTemplate")
    public ResultEntity<String> syncColumnConstantsTemplate(@RequestBody ColumnNameConstants columnNameConstants) {
        if (columnNameConstants != null) {
            log.info("设置Excel列名:{}", columnNameConstants);
            ColumnNameConstants.getInstance().setColumnNames(columnNameConstants);
        }
        return ResultEntity.ok();
    }

    @PostMapping("/insertExcelCaseData")
    //FIXME：耗时
    public ResultEntity<String> insertExcelCaseData(@RequestBody List<String> sheetNameList) {
        long st = System.currentTimeMillis();
        log.info("Excel开始保存到数据库:{}", sheetNameList);
        if (sheetNameList == null || sheetNameList.isEmpty()) return ResultEntity.fail("Excel表名为空");
        Map<String, ExcelSheetTable> map = tableMap.entrySet().stream()
                .filter(entry -> sheetNameList.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        dataMap.put("excelSheetData", map);
        excelDataService.insertData(dataMap);
        log.info("Excel保存数据库成功，耗时:{}s", (System.currentTimeMillis() - st) / 1000.0);
        return ResultEntity.ok();
    }

    @PostMapping("/sheetNames")
    public ResultEntity<List<String>> getSheetNames(@RequestSingleParam String excelPath) {
        List<String> sheetNames = ExcelUtils.getSheetNames(excelPath);
        log.info("获取Excel表格名称：{}（案例:{}）", sheetNames, excelPath);
        return ResultEntity.ok(sheetNames);
    }

    @PostMapping("/exportExcelCase")
    public ResultEntity<String> exportCaseReport(@RequestBody ExcelEntity excelEntity) {
        long st = System.currentTimeMillis();
        log.info("导出Excel数据:{}", excelEntity);
        String exportFilePath = excelEntity.getExportType().equals(EXPORT_ORIGINAL_FILE.name()) ?
                excelEntity.getOriginalFilePath() : excelEntity.getExportFilePath();
        Map<String, List<ExcelDataEntity>> sheetDataInfoMap = new LinkedHashMap<>();
//        String excelPath = excelEntity.isReloadProject() ? excelEntity.getTemplateFilePath() : excelEntity.getOriginalFilePath();
        for (String sheetName : excelEntity.getSheetNames()) {
            List<ExcelDataEntity> excelData = excelDataService.findExcelDataByName(sheetName);
            sheetDataInfoMap.put(sheetName, excelData);
        }
        log.info("EXCEL导出->从数据库导出全部Excel数据，耗时:{}s", (System.currentTimeMillis() - st) / 1000.0);
        st = System.currentTimeMillis();
        ResultEntity<String> resultEntity = ExcelBuilder.getInstance().exportExcelCaseFile(excelEntity.getTemplateFilePath(), exportFilePath, sheetDataInfoMap, excelEntity.getHeaderRowNumber());
        log.info("EXCEL导出->保存Excel文件，耗时:{}s", (System.currentTimeMillis() - st) / 1000.0);
        return resultEntity;
    }

    /**
     * 排序后重新写入数据库
     *
     * @param excelRowDataModels
     * @return
     */
    @PostMapping("/updateOrderExcelCase")
    @LogExecutionTime
    public ResultEntity<String> updateOrderExcelCase(@RequestBody List<ExcelRowDataModel> excelRowDataModels) {
        log.info("更新Excel数据行顺序:{}", excelRowDataModels);
        if (excelRowDataModels.isEmpty()) return ResultEntity.fail();
        ExcelRowDataModel excelRowDataModel = excelRowDataModels.get(0);
        String excelFileName = excelRowDataModel.getExcelFileName();
        String sheetName = excelRowDataModel.getSheetName();
        excelDataService.deleteExcelDataByName(sheetName);
        log.info("脱拽表重排序: 删除Excel数据表成功");
        excelDataService.insertToExcelDataTable(excelFileName, sheetName, excelRowDataModels);
        return ResultEntity.ok();
    }

    /**
     * 更新单行数据写入数据库
     *
     * @param excelRowDataModel
     * @return
     */
    //FIXME：单步调试也会调用
    @PostMapping("/updateExcelCase")
    public ResultEntity<String> updateExcelCase(@RequestBody ExcelRowDataModel excelRowDataModel) {
        log.info("更新Excel数据行:{}", excelRowDataModel);
        excelDataService.updateExcelData(excelRowDataModel);
        return ResultEntity.ok();
    }

    /**
     * 清空测试人员时间等数据，并更新数据库
     *
     * @param columnDataMap
     * @return
     */
    @PostMapping("/clearMapColumnData")
    @LogExecutionTime
    public ResultEntity<String> clearMapColumnData(@RequestBody Map<String, Object> columnDataMap) {
        log.info("清空Excel列数据:{}", columnDataMap);
        //找到sheetId，删除对应的column值
        excelDataService.clearMapColumnData(columnDataMap);
        return ResultEntity.ok();
    }

    @PostMapping("/deleteExcelCaseTable")
    @LogExecutionTime
    public ResultEntity<String> deleteExcelCaseTable(@RequestBody Map<String, String> dataMap) {
        log.info("删除Excel数据:{}", dataMap);
        excelDataService.deleteExcelDataByName(dataMap.get("sheetName"));
        return ResultEntity.ok();
    }

}
